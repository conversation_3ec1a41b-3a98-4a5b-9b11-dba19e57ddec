/**
 * User Management API Client
 *
 * This module provides a client-side interface for the user management API.
 * It handles authentication, error handling, and data formatting.
 */
import apiFetch from './api-fetch';
import { getApiUrl } from './cross-origin';

/**
 * Fetch users with optional filtering, sorting, and pagination
 *
 * @param {Object} options - Query options
 * @param {number} options.page - Page number (default: 1)
 * @param {number} options.limit - Items per page (default: 10)
 * @param {string} options.sortBy - Sort field (default: 'created_at')
 * @param {string} options.sortOrder - Sort order ('asc' or 'desc', default: 'desc')
 * @param {string} options.search - Search query (optional)
 * @param {string} options.role - Role filter (optional)
 * @returns {Promise<Object>} - { users, total, page, limit, pages }
 */
export async function fetchUsers({
  page = 1,
  limit = 10,
  sortBy = 'created_at',
  sortOrder = 'desc',
  search = '',
  role = 'all'
} = {}) {
  try {
    console.log('User Management: Fetching users with options:', { page, limit, sortBy, sortOrder, search, role });

    // Build query string
    const queryParams = new URLSearchParams({
      page,
      limit,
      sortBy,
      sortOrder
    });

    if (search) {
      queryParams.append('search', search);
    }

    if (role && role !== 'all') {
      queryParams.append('role', role);
    }

    // Make API request with proper URL
    const apiUrl = getApiUrl(`/api/admin/users/list?${queryParams.toString()}`);
    console.log('User Management: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl);

    console.log('User Management: Fetched users successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error fetching users:', error);
    throw error;
  }
}

/**
 * Update a user's role
 *
 * @param {string} userId - User ID
 * @param {string} role - New role
 * @returns {Promise<Object>} - Updated user
 */
export async function updateUserRole(userId, role) {
  try {
    console.log(`User Management: Updating role for user ${userId} to ${role}`);

    // Prepare API URL
    const apiUrl = getApiUrl('/api/admin/users/set-role');
    console.log('User Management: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify({ userId, role })
    });

    console.log('User Management: Role updated successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error updating role:', error);
    throw error;
  }
}

/**
 * Create a new user
 *
 * @param {Object} userData - User data
 * @param {string} userData.email - Email address
 * @param {string} userData.password - Password
 * @param {string} userData.role - Role
 * @param {string} userData.name - Name (optional)
 * @returns {Promise<Object>} - Created user
 */
export async function createUser({ email, password, role, name }) {
  try {
    console.log(`User Management: Creating new user with email ${email}`);

    // Prepare API URL
    const apiUrl = getApiUrl('/api/admin/users/create');
    console.log('User Management: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify({ email, password, role, name })
    });

    console.log('User Management: User created successfully');
    return response;
  } catch (error) {
    console.error('User Management: Error creating user:', error);
    throw error;
  }
}

export default {
  fetchUsers,
  updateUserRole,
  createUser
};
