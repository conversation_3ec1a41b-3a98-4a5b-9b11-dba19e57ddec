import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getAutomations(req, res)
    case 'POST':
      return createAutomation(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get marketing automations with optional filters
async function getAutomations(req, res) {
  const {
    search,
    trigger_type,
    message_type,
    is_active,
    sort_by = 'created_at',
    sort_order = 'desc',
    limit = 50,
    offset = 0
  } = req.query

  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    let query = client
      .from('marketing_automations')
      .select(`
        *,
        template:template_id (id, name),
        segment:segment_id (id, name)
      `, { count: 'exact' })

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    // Apply trigger type filter
    if (trigger_type) {
      query = query.eq('trigger_type', trigger_type)
    }

    // Apply message type filter
    if (message_type) {
      query = query.eq('message_type', message_type)
    }

    // Apply active status filter
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active === 'true')
    }

    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })

    // Apply pagination
    if (limit) {
      query = query.limit(limit)
    }
    if (offset) {
      query = query.offset(offset)
    }

    // Execute query
    const { data, error, count } = await query

    if (error) {
      throw error
    }

    // Get execution stats for each automation
    const automationsWithStats = await Promise.all(
      data.map(async (automation) => {
        // Get total executions
        const { count: totalExecutions, error: totalError } = await client
          .from('automation_logs')
          .select('id', { count: 'exact' })
          .eq('automation_id', automation.id)

        // Get successful executions
        const { count: successfulExecutions, error: successError } = await client
          .from('automation_logs')
          .select('id', { count: 'exact' })
          .eq('automation_id', automation.id)
          .eq('status', 'success')

        // Get last execution
        const { data: lastExecution, error: lastError } = await client
          .from('automation_logs')
          .select('sent_at, status')
          .eq('automation_id', automation.id)
          .order('sent_at', { ascending: false })
          .limit(1)
          .single()

        return {
          ...automation,
          stats: {
            total_executions: totalError ? 0 : totalExecutions || 0,
            successful_executions: successError ? 0 : successfulExecutions || 0,
            last_execution: lastError ? null : lastExecution
          }
        }
      })
    )

    return res.status(200).json({
      automations: automationsWithStats,
      total: count
    })
  } catch (error) {
    console.error('Error fetching automations:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Create a new marketing automation
async function createAutomation(req, res) {
  const {
    name,
    description,
    trigger_type,
    trigger_config,
    template_id,
    message_type,
    subject,
    content,
    segment_id,
    is_active = false
  } = req.body

  try {
    // Get current user
    const { user } = await getCurrentUser(req)

    // Validate required fields
    if (!name || !trigger_type || !trigger_config || !message_type || !content) {
      return res.status(400).json({ error: 'Name, trigger type, trigger configuration, message type, and content are required' })
    }

    // Validate trigger type
    const validTriggerTypes = ['event', 'schedule', 'segment_entry']
    if (!validTriggerTypes.includes(trigger_type)) {
      return res.status(400).json({ error: 'Invalid trigger type. Must be one of: event, schedule, segment_entry' })
    }

    // Validate message type
    const validMessageTypes = ['email', 'sms', 'push']
    if (!validMessageTypes.includes(message_type)) {
      return res.status(400).json({ error: 'Invalid message type. Must be one of: email, sms, push' })
    }

    // Email messages require a subject
    if (message_type === 'email' && !subject) {
      return res.status(400).json({ error: 'Subject is required for email messages' })
    }

    // Validate trigger configuration
    try {
      validateTriggerConfig(trigger_type, trigger_config)
    } catch (validationError) {
      return res.status(400).json({ error: validationError.message })
    }

    // Create automation
    const { data, error } = await client
      .from('marketing_automations')
      .insert([
        {
          name,
          description,
          trigger_type,
          trigger_config,
          template_id,
          message_type,
          subject,
          content,
          segment_id,
          is_active,
          created_by: user.id
        }
      ])
      .select()

    if (error) {
      throw error
    }

    return res.status(201).json(data[0])
  } catch (error) {
    console.error('Error creating automation:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to validate trigger configuration
function validateTriggerConfig(triggerType, triggerConfig) {
  if (typeof triggerConfig !== 'object') {
    throw new Error('Trigger configuration must be an object')
  }

  switch (triggerType) {
    case 'event':
      if (!triggerConfig.event_type) {
        throw new Error('Event type is required for event triggers')
      }
      break
    case 'schedule':
      if (!triggerConfig.frequency) {
        throw new Error('Frequency is required for schedule triggers')
      }
      break
    case 'segment_entry':
      if (!triggerConfig.delay_hours && triggerConfig.delay_hours !== 0) {
        throw new Error('Delay hours is required for segment entry triggers')
      }
      break
    default:
      throw new Error(`Unknown trigger type: ${triggerType}`)
  }
}
