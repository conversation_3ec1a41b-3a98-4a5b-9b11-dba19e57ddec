import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { period = 'month', start_date, end_date } = req.query

  try {
    // Calculate date range based on period
    const today = new Date()
    let startDate = new Date()
    let endDate = new Date()

    if (start_date && end_date) {
      // Custom date range
      startDate = new Date(start_date)
      endDate = new Date(end_date)
    } else {
      // Predefined periods
      switch (period) {
        case 'week':
          startDate.setDate(today.getDate() - 7)
          break
        case 'month':
          startDate.setMonth(today.getMonth() - 1)
          break
        case 'quarter':
          startDate.setMonth(today.getMonth() - 3)
          break
        case 'year':
          startDate.setFullYear(today.getFullYear() - 1)
          break
        default:
          startDate.setMonth(today.getMonth() - 1) // Default to last month
      }
    }

    // Format dates for Supabase queries
    const formattedStartDate = startDate.toISOString()
    const formattedEndDate = endDate.toISOString()
    
    // Get client from Supabase
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available for analytics engagement.");
      return res.status(503).json({ error: 'Service temporarily unavailable. Please try again later.' });
    }

    // Get all customers
    const { data: allCustomers, error: customersError } = await client
      .from('customers')
      .select('id, marketing_consent, created_at')

    if (customersError) throw customersError

    // Get new customers in the period
    const { data: newCustomers, error: newCustomersError } = await client
      .from('customers')
      .select('id, marketing_consent, created_at')
      .gte('created_at', formattedStartDate)
      .lte('created_at', formattedEndDate)

    if (newCustomersError) throw newCustomersError

    // Get bookings in the period
    const { data: bookings, error: bookingsError } = await client
      .from('bookings')
      .select('id, customer_id, created_at')
      .gte('created_at', formattedStartDate)
      .lte('created_at', formattedEndDate)

    if (bookingsError) throw bookingsError

    // Get orders in the period
    const { data: orders, error: ordersError } = await client
      .from('orders')
      .select('id, customer_id, created_at, total_amount')
      .gte('created_at', formattedStartDate)
      .lte('created_at', formattedEndDate)

    if (ordersError) throw ordersError

    // Calculate customer engagement metrics
    const totalCustomers = allCustomers ? allCustomers.length : 0
    const totalNewCustomers = newCustomers ? newCustomers.length : 0
    const customersWithConsent = allCustomers 
      ? allCustomers.filter(c => c.marketing_consent).length 
      : 0
    const newCustomersWithConsent = newCustomers 
      ? newCustomers.filter(c => c.marketing_consent).length 
      : 0

    // Calculate active customers (made a booking or order in the period)
    const activeCustomerIds = new Set()
    bookings?.forEach(booking => {
      if (booking.customer_id) activeCustomerIds.add(booking.customer_id)
    })
    orders?.forEach(order => {
      if (order.customer_id) activeCustomerIds.add(order.customer_id)
    })
    const activeCustomers = activeCustomerIds.size

    // Calculate repeat customers (made more than one booking or order)
    const customerActivityCount = {}
    bookings?.forEach(booking => {
      if (booking.customer_id) {
        customerActivityCount[booking.customer_id] = 
          (customerActivityCount[booking.customer_id] || 0) + 1
      }
    })
    orders?.forEach(order => {
      if (order.customer_id) {
        customerActivityCount[order.customer_id] = 
          (customerActivityCount[order.customer_id] || 0) + 1
      }
    })
    const repeatCustomers = Object.values(customerActivityCount).filter(count => count > 1).length

    // Calculate time series data for new customers
    const timeSeriesData = calculateTimeSeriesData(
      newCustomers || [],
      startDate,
      endDate,
      period
    )

    // Prepare response
    const response = {
      period,
      date_range: {
        start_date: formattedStartDate,
        end_date: formattedEndDate
      },
      summary: {
        total_customers: totalCustomers,
        new_customers: totalNewCustomers,
        active_customers: activeCustomers,
        repeat_customers: repeatCustomers,
        customers_with_consent: customersWithConsent,
        new_customers_with_consent: newCustomersWithConsent,
        consent_rate: calculatePercentage(customersWithConsent, totalCustomers),
        new_customer_consent_rate: calculatePercentage(newCustomersWithConsent, totalNewCustomers),
        customer_growth_rate: calculatePercentage(totalNewCustomers, totalCustomers)
      },
      time_series: timeSeriesData
    }

    return res.status(200).json(response)
  } catch (error) {
    console.error('Error fetching engagement analytics:', error)
    return res.status(500).json({ error: 'Failed to fetch engagement analytics' })
  }
}

// Helper function to calculate percentage
function calculatePercentage(numerator, denominator) {
  if (!denominator) return 0
  return Math.round((numerator / denominator) * 100)
}

// Helper function to calculate time series data for new customers
function calculateTimeSeriesData(customers, startDate, endDate, period) {
  const result = []
  const dateFormat = new Intl.DateTimeFormat('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: period === 'year' ? 'numeric' : undefined
  })

  // Determine interval based on period
  let interval = 'day'
  if (period === 'year') {
    interval = 'month'
  } else if (period === 'quarter') {
    interval = 'week'
  }

  // Create date buckets
  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    const nextDate = new Date(currentDate)
    
    // Increment based on interval
    if (interval === 'day') {
      nextDate.setDate(currentDate.getDate() + 1)
    } else if (interval === 'week') {
      nextDate.setDate(currentDate.getDate() + 7)
    } else if (interval === 'month') {
      nextDate.setMonth(currentDate.getMonth() + 1)
    }

    // Count customers in this interval
    const customersInInterval = customers.filter(customer => {
      const createdDate = new Date(customer.created_at)
      return createdDate >= currentDate && createdDate < nextDate
    })

    result.push({
      date: dateFormat.format(currentDate),
      new_customers: customersInInterval.length,
      with_consent: customersInInterval.filter(c => c.marketing_consent).length
    })

    currentDate.setTime(nextDate.getTime())
  }

  return result
}
