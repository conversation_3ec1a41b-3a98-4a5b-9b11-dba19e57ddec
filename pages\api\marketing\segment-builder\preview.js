import { getAdminClient } from '@/lib/supabase'

import { getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Preview customers based on segment query
  const { segment_query } = req.body

  if (!segment_query) {
    return res.status(400).json({ error: 'Segment query is required' })
  }

  try {
    // Build query from segment conditions
    const query = await buildSegmentQuery(segment_query)
    
    // Get total count
    const { count, error: countError } = await query.select('id', { count: 'exact', head: true })
    
    if (countError) {
      throw countError
    }
    
    // Get sample customers (limited to 10 for preview)
    const { data: customers, error: customersError } = await query
      .select('id, name, email, phone, city, state, marketing_consent')
      .limit(10)
    
    if (customersError) {
      throw customersError
    }

    return res.status(200).json({
      customers: customers || [],
      total: count || 0
    })
  } catch (error) {
    console.error('Error previewing segment:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to build a Supabase query from segment conditions
async function buildSegmentQuery(segmentQuery) {
  const client = getClient();
  if (!client) {
    console.error("Supabase client not available.");
    throw new Error('Database connection failed');
  }
  
  let query = client.from('customers').select('*')
  
  if (!segmentQuery || !segmentQuery.groups || segmentQuery.groups.length === 0) {
    return query
  }
  
  // Process each group
  segmentQuery.groups.forEach((group, groupIndex) => {
    const { operator, conditions } = group
    
    if (!conditions || conditions.length === 0) {
      return
    }
    
    // Start a new filter group
    let filterExpression = ''
    
    // Process each condition in the group
    conditions.forEach((condition, conditionIndex) => {
      const { type, field, operator: condOperator, value } = condition
      
      // Skip invalid conditions
      if (!field || !condOperator) {
        return
      }
      
      let conditionFilter = ''
      
      // Build filter based on condition type and operator
      if (type === 'customer') {
        // Direct customer field filters
        if (field === 'marketing_consent') {
          conditionFilter = `${field}.eq.${value}`
        } else if (['city', 'state', 'country'].includes(field)) {
          switch (condOperator) {
            case 'equals':
              conditionFilter = `${field}.eq.${value}`
              break
            case 'not_equals':
              conditionFilter = `${field}.neq.${value}`
              break
            case 'contains':
              conditionFilter = `${field}.ilike.%${value}%`
              break
            case 'in':
              // Handle comma-separated values
              const inValues = value.split(',').map(v => v.trim())
              conditionFilter = `${field}.in.(${inValues.join(',')})`
              break
            case 'not_in':
              const notInValues = value.split(',').map(v => v.trim())
              conditionFilter = `${field}.not.in.(${notInValues.join(',')})`
              break
          }
        }
      } else if (type === 'booking') {
        // Booking-related filters require joins or subqueries
        // This is a simplified implementation
        if (field === 'booking_count') {
          // This would require a more complex query in a real implementation
          // For now, we'll use a placeholder
          conditionFilter = `id.in.(SELECT customer_id FROM bookings GROUP BY customer_id HAVING COUNT(*) ${getOperatorSymbol(condOperator)} ${value})`
        } else if (field === 'service_id') {
          conditionFilter = `id.in.(SELECT customer_id FROM bookings WHERE service_id = '${value}')`
        }
      } else if (type === 'time') {
        // Time-based filters
        if (field === 'created_at') {
          const dateValue = parseTimeValue(value)
          switch (condOperator) {
            case 'greater_than':
              conditionFilter = `${field}.gte.${dateValue}`
              break
            case 'less_than':
              conditionFilter = `${field}.lte.${dateValue}`
              break
          }
        }
      }
      
      // Add the condition to the filter expression
      if (conditionFilter) {
        if (filterExpression) {
          filterExpression += `,${conditionFilter}`
        } else {
          filterExpression = conditionFilter
        }
      }
    })
    
    // Add the group filter to the query
    if (filterExpression) {
      if (groupIndex === 0) {
        query = query.or(filterExpression)
      } else {
        // For subsequent groups, we need to use the logical operator
        if (operator === 'and') {
          query = query.and(filterExpression)
        } else {
          query = query.or(filterExpression)
        }
      }
    }
  })
  
  return query
}

// Helper function to get operator symbol
function getOperatorSymbol(operator) {
  switch (operator) {
    case 'equals':
      return '='
    case 'not_equals':
      return '!='
    case 'greater_than':
      return '>'
    case 'less_than':
      return '<'
    case 'between':
      return 'BETWEEN'
    default:
      return '='
  }
}

// Helper function to parse time values like "30d" (30 days)
function parseTimeValue(value) {
  if (!value) return null
  
  // If it's already a date string, return it
  if (value.includes('-') || value.includes('/')) {
    return value
  }
  
  // Parse time periods like "30d", "6m", "1y"
  const match = value.match(/^(\d+)([dmy])$/)
  if (match) {
    const amount = parseInt(match[1])
    const unit = match[2]
    
    const date = new Date()
    
    switch (unit) {
      case 'd': // days
        date.setDate(date.getDate() - amount)
        break
      case 'm': // months
        date.setMonth(date.getMonth() - amount)
        break
      case 'y': // years
        date.setFullYear(date.getFullYear() - amount)
        break
    }
    
    return date.toISOString()
  }
  
  return value
}
