import supabase, { getCurrentUser } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser()
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCustomer(id, res)
    case 'PUT':
      return updateCustomer(id, req, res)
    case 'DELETE':
      // Only admins can delete customers
      try {
        const { role } = await getCurrentUser()
        if (role !== 'admin') {
          return res.status(403).json({ error: 'Forbidden' })
        }
      } catch (error) {
        return res.status(403).json({ error: 'Authorization failed' })
      }
      return deleteCustomer(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single customer with their booking history
async function getCustomer(id, res) {
  try {
    // Use Supabase client directly
    const client = supabase;

    // Get customer details
    const { data: customer, error } = await client
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      throw error
    }

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' })
    }

    // Get customer booking history
    const { data: bookings, error: bookingsError } = await client
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        location,
        notes,
        services:service_id (id, name, price, duration, color)
      `)
      .eq('customer_id', id)
      .order('start_time', { ascending: false })

    if (bookingsError) {
      throw bookingsError
    }

    // Get customer preferences
    const { data: preferences, error: preferencesError } = await client
      .from('customer_preferences')
      .select('*')
      .eq('customer_id', id)

    if (preferencesError) {
      throw preferencesError
    }

    // Return customer with booking history and preferences
    return res.status(200).json({
      customer,
      bookings: bookings || [],
      preferences: preferences || []
    })
  } catch (error) {
    console.error('Error fetching customer:', error)
    return res.status(error.code === 'PGRST116' ? 404 : 500).json({
      error: error.code === 'PGRST116' ? 'Customer not found' : 'Failed to fetch customer'
    })
  }
}

// Update a customer
async function updateCustomer(id, req, res) {
  try {
    // Use Supabase client directly

    const {
      name,
      email,
      phone,
      address,
      city,
      state,
      postal_code,
      country,
      notes,
      marketing_consent,
      preferences
    } = req.body

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({ error: 'Name and email are required' })
    }

    // Use Supabase client directly
    const client = supabase;

    // Check if email already exists (for a different customer)
    const { data: existingCustomer, error: checkError } = await client
      .from('customers')
      .select('id')
      .eq('email', email)
      .neq('id', id)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError
    }

    if (existingCustomer) {
      return res.status(409).json({ error: 'Another customer with this email already exists' })
    }

    // Update customer
    const { data, error } = await client
      .from('customers')
      .update({
        name,
        email,
        phone,
        address,
        city,
        state,
        postal_code,
        country,
        notes,
        marketing_consent,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Customer not found' })
    }

    // Update preferences if provided
    if (preferences && Array.isArray(preferences)) {
      // Delete existing preferences
      await client
        .from('customer_preferences')
        .delete()
        .eq('customer_id', id)

      // Insert new preferences
      if (preferences.length > 0) {
        const preferencesData = preferences.map(pref => ({
          customer_id: id,
          preference_key: pref.key,
          preference_value: pref.value
        }))

        const { error: prefError } = await client
          .from('customer_preferences')
          .insert(preferencesData)

        if (prefError) {
          throw prefError
        }
      }
    }

    return res.status(200).json(data[0])
  } catch (error) {
    console.error('Error updating customer:', error)
    return res.status(500).json({ error: 'Failed to update customer' })
  }
}

// Delete a customer
async function deleteCustomer(id, res) {
  try {
    // Use Supabase client directly
    const client = supabase;

    // Delete customer (preferences will be deleted via cascade)
    const { data, error } = await client
      .from('customers')
      .delete()
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Customer not found' })
    }

    return res.status(200).json({ message: 'Customer deleted successfully' })
  } catch (error) {
    console.error('Error deleting customer:', error)
    return res.status(500).json({ error: 'Failed to delete customer' })
  }
}
