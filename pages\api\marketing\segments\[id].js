import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getSegment(id, req, res)
    case 'PUT':
      return updateSegment(id, req, res)
    case 'DELETE':
      // Only admins can delete segments
      try {
        const { role } = await getCurrentUser(req)
        if (role !== 'admin') {
          return res.status(403).json({ error: 'Forbidden' })
        }
      } catch (error) {
        return res.status(403).json({ error: 'Authorization failed' })
      }
      return deleteSegment(id, res)
    case 'POST':
      if (req.query.action === 'preview') {
        return previewSegment(id, req, res)
      }
      return res.status(400).json({ error: 'Invalid action' })
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single segment with customer count
async function getSegment(id, req, res) {
  try {
    const client = getClient();
    if (!client) {
      console.error("Supabase client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Get segment details
    const { data: segment, error } = await client
      .from('customer_segments')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      throw error
    }

    if (!segment) {
      return res.status(404).json({ error: 'Segment not found' })
    }

    // Get customer count
    const customerCount = await getSegmentCustomerCount(segment)

    // Return segment with customer count
    return res.status(200).json({
      segment: {
        ...segment,
        customer_count: customerCount
      }
    })
  } catch (error) {
    console.error('Error fetching segment:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Update a segment
async function updateSegment(id, req, res) {
  const { name, description, segment_query } = req.body

  try {
    // Validate required fields
    if (!name || !segment_query) {
      return res.status(400).json({ error: 'Name and segment query are required' })
    }

    // Update segment
    const { data, error } = await supabase
      .from('customer_segments')
      .update({
        name,
        description,
        segment_query,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Segment not found' })
    }

    return res.status(200).json(data[0])
  } catch (error) {
    console.error('Error updating segment:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Delete a segment
async function deleteSegment(id, res) {
  try {
    // Check if segment is used in any campaigns
    const { data: campaigns, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('id')
      .eq('target_segment', id)
      .limit(1)

    if (campaignError) {
      throw campaignError
    }

    if (campaigns && campaigns.length > 0) {
      return res.status(400).json({ 
        error: 'Cannot delete segment that is used in campaigns',
        campaigns: campaigns
      })
    }

    // Delete segment
    const { error } = await supabase
      .from('customer_segments')
      .delete()
      .eq('id', id)

    if (error) {
      throw error
    }

    return res.status(200).json({ success: true })
  } catch (error) {
    console.error('Error deleting segment:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Preview customers in a segment
async function previewSegment(id, req, res) {
  try {
    // Get segment details
    const { data: segment, error } = await supabase
      .from('customer_segments')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      throw error
    }

    if (!segment) {
      return res.status(404).json({ error: 'Segment not found' })
    }

    // Build query from segment conditions
    const query = buildSegmentQuery(segment.segment_query)
    
    // Get total count
    const { count, error: countError } = await query.select('id', { count: 'exact', head: true })
    
    if (countError) {
      throw countError
    }
    
    // Get sample customers (limited to 10 for preview)
    const { data: customers, error: customersError } = await query
      .select('id, name, email, phone, city, state, marketing_consent')
      .limit(10)
    
    if (customersError) {
      throw customersError
    }

    return res.status(200).json({
      segment,
      customers: customers || [],
      total: count || 0
    })
  } catch (error) {
    console.error('Error previewing segment:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to get customer count for a segment
async function getSegmentCustomerCount(segment) {
  try {
    const query = buildSegmentQuery(segment.segment_query)
    const { count } = await query.select('id', { count: 'exact', head: true })
    return count || 0
  } catch (error) {
    console.error(`Error getting customer count for segment ${segment.id}:`, error)
    return 0
  }
}

// Helper function to build a Supabase query from segment conditions
function buildSegmentQuery(segmentQuery) {
  let query = supabase.from('customers').select('*')
  
  if (!segmentQuery || !segmentQuery.groups || segmentQuery.groups.length === 0) {
    return query
  }
  
  // Process each group
  segmentQuery.groups.forEach((group, groupIndex) => {
    const { operator, conditions } = group
    
    if (!conditions || conditions.length === 0) {
      return
    }
    
    // Start a new filter group
    let filterExpression = ''
    
    // Process each condition in the group
    conditions.forEach((condition, conditionIndex) => {
      const { type, field, operator: condOperator, value } = condition
      
      // Skip invalid conditions
      if (!field || !condOperator) {
        return
      }
      
      let conditionFilter = ''
      
      // Build filter based on condition type and operator
      if (type === 'customer') {
        // Direct customer field filters
        if (field === 'marketing_consent') {
          conditionFilter = `${field}.eq.${value}`
        } else if (['city', 'state', 'country'].includes(field)) {
          switch (condOperator) {
            case 'equals':
              conditionFilter = `${field}.eq.${value}`
              break
            case 'not_equals':
              conditionFilter = `${field}.neq.${value}`
              break
            case 'contains':
              conditionFilter = `${field}.ilike.%${value}%`
              break
            case 'in':
              // Handle comma-separated values
              const inValues = value.split(',').map(v => v.trim())
              conditionFilter = `${field}.in.(${inValues.join(',')})`
              break
            case 'not_in':
              const notInValues = value.split(',').map(v => v.trim())
              conditionFilter = `${field}.not.in.(${notInValues.join(',')})`
              break
          }
        }
      } else if (type === 'booking') {
        // Booking-related filters require joins or subqueries
        // This is a simplified implementation
        if (field === 'booking_count') {
          // This would require a more complex query in a real implementation
          // For now, we'll use a placeholder
          conditionFilter = `id.in.(SELECT customer_id FROM bookings GROUP BY customer_id HAVING COUNT(*) ${getOperatorSymbol(condOperator)} ${value})`
        } else if (field === 'service_id') {
          conditionFilter = `id.in.(SELECT customer_id FROM bookings WHERE service_id = '${value}')`
        }
      } else if (type === 'time') {
        // Time-based filters
        if (field === 'created_at') {
          const dateValue = parseTimeValue(value)
          switch (condOperator) {
            case 'greater_than':
              conditionFilter = `${field}.gte.${dateValue}`
              break
            case 'less_than':
              conditionFilter = `${field}.lte.${dateValue}`
              break
          }
        }
      }
      
      // Add the condition to the filter expression
      if (conditionFilter) {
        if (filterExpression) {
          filterExpression += `,${conditionFilter}`
        } else {
          filterExpression = conditionFilter
        }
      }
    })
    
    // Add the group filter to the query
    if (filterExpression) {
      if (groupIndex === 0) {
        query = query.or(filterExpression)
      } else {
        // For subsequent groups, we need to use the logical operator
        if (operator === 'and') {
          query = query.and(filterExpression)
        } else {
          query = query.or(filterExpression)
        }
      }
    }
  })
  
  return query
}

// Helper function to get operator symbol
function getOperatorSymbol(operator) {
  switch (operator) {
    case 'equals':
      return '='
    case 'not_equals':
      return '!='
    case 'greater_than':
      return '>'
    case 'less_than':
      return '<'
    case 'between':
      return 'BETWEEN'
    default:
      return '='
  }
}

// Helper function to parse time values like "30d" (30 days)
function parseTimeValue(value) {
  if (!value) return null
  
  // If it's already a date string, return it
  if (value.includes('-') || value.includes('/')) {
    return value
  }
  
  // Parse time periods like "30d", "6m", "1y"
  const match = value.match(/^(\d+)([dmy])$/)
  if (match) {
    const amount = parseInt(match[1])
    const unit = match[2]
    
    const date = new Date()
    
    switch (unit) {
      case 'd': // days
        date.setDate(date.getDate() - amount)
        break
      case 'm': // months
        date.setMonth(date.getMonth() - amount)
        break
      case 'y': // years
        date.setFullYear(date.getFullYear() - amount)
        break
    }
    
    return date.toISOString()
  }
  
  return value
}
