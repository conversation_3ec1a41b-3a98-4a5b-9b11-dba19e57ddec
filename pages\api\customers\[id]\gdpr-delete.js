import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Only admins can perform GDPR deletions
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' })
    }
  } catch (error) {
    return res.status(403).json({ error: 'Authorization failed' })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { id } = req.query

  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get customer details for logging
    const { data: customer, error: customerError } = await client
      .from('customers')
      .select('email')
      .eq('id', id)
      .single()

    if (customerError) {
      throw customerError
    }

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' })
    }

    // Anonymize customer data
    const { error: updateError } = await client
      .from('customers')
      .update({
        name: 'Anonymized User',
        email: `anonymized-${Date.now()}@deleted.oceansoulsparkles.com.au`,
        phone: null,
        address: null,
        city: null,
        state: null,
        postal_code: null,
        country: null,
        notes: 'Data deleted per GDPR request',
        marketing_consent: false,
        updated_at: new Date()
      })
      .eq('id', id)

    if (updateError) {
      throw updateError
    }

    // Delete customer preferences
    const { error: prefError } = await client
      .from('customer_preferences')
      .delete()
      .eq('customer_id', id)

    if (prefError) {
      throw prefError
    }

    // Log the GDPR deletion request
    const { error: logError } = await client
      .from('gdpr_deletion_log')
      .insert([
        {
          customer_id: id,
          original_email: customer.email,
          requested_by: req.body.requested_by || 'admin',
          reason: req.body.reason || 'Customer request'
        }
      ])

    if (logError) {
      console.error('Error logging GDPR deletion:', logError)
      // Continue even if logging fails
    }

    return res.status(200).json({ message: 'Customer data anonymized successfully' })
  } catch (error) {
    console.error('Error processing GDPR deletion:', error)
    return res.status(500).json({ error: 'Failed to process GDPR deletion request' })
  }
}
